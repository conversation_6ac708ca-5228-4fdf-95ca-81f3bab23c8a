<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ref, onMounted, watch } from 'vue'
import { useAuth } from '@/composables/useAuth'
import { useShows } from '@/composables/useShows'
import { db } from '@/firebase'
import { Show } from '@/classes/Show'
import TheHeader from '@/components/layout/TheHeader.vue'
import MigrationPrompt from '@/components/MigrationPrompt.vue'
import { collection, query, where, getDocs } from 'firebase/firestore'


const { showMigrationPrompt, user } = useAuth()
const { migrateToCloud, shows, loadShow } = useShows()

const showsClass = ref<Show[]>([])

async function fetchShows() {
  // Fetch shows from the database
  const showsRef = collection(db, 'shows')
  if (user.value) {
    // Get shows belonging to current user
    const showsQuery = query(showsRef, where('createdBy', '==', user.value.uid))

    const showsSnapshot = await getDocs(showsQuery)
    showsClass.value = showsSnapshot.docs.map(doc => new Show(doc))
  } else {
    // Fetch shows from local storage into showsClass array without using loadShow funciton
    const data = localStorage.getItem('show-setlists')
    if (data) {
      // map over shows and push new Show objects to showsClass array
      const parsedShows = JSON.parse(data)
      showsClass.value = parsedShows.map((show: any) => new Show(show))
    } else {
      console.log('No shows found in local storage')
    }
  }
}

/**
 * Set the document title.
 */
onMounted(async () => {
  document.title = 'Set List Creator'
  await fetchShows()
  // TODO: Implement shows as a Class.
})

/**
 * Watch for changes in the user state and show the migration prompt if there are local shows.
 * 
 * @param {User} user The user state.
 * @param {User} newUser The new user state.
 */
watch(user, (newUser) => {
  if (newUser) {
    const localShows = shows.value.filter(show => show.storageLocation === 'local')
    if (localShows.length > 0) showMigrationPrompt.value = true
  } else {
    showMigrationPrompt.value = false
  }
})

/**
 * Migrate a list of shows to the cloud.
 *
 * @param {string[]} ids The show IDs to migrate.
 */
const handleMigrate = async (ids: string[]) => {
  for (const id of ids) {
    const show = loadShow(id)
    if (show) {
      await migrateToCloud(show)
    }
  }
}
</script>

<template>
  <div class="app">
    <TheHeader />
    <RouterView />
    <MigrationPrompt v-if="showMigrationPrompt" @close="showMigrationPrompt = false" @migrate="handleMigrate" />
  </div>
</template>

<style scoped>
.app {
  min-height: 100vh;
  background: var(--color-background);
}
</style>