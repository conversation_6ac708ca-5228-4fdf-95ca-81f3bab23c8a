<script setup lang="ts">
import { useAuth } from '@/composables/useAuth'
import { useSongs } from '@/composables/useSongs'
import SongItem from '@/components/SongItem.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const { user } = useAuth()
const { songs, createSong } = useSongs()

const handleNew = async () => {
  const newSong = await createSong({
    title: 'New Song',
    key: 'C'
  })

  router.push({ name: 'edit-song', params: { id: newSong.id }, query: { edit: 'true' } })
}
</script>

<template>
  <div class="songs-list">
    <div class="songs-header">
      <div>
        <h1>Songs</h1>
        <p class="meta">
          <span>{{ songs.length }} songs</span>
          <span class="separator">•</span>
          <span>{{ user?.email }}</span>
        </p>
      </div>
      <BaseButton @click="handleNew" variant="primary">Add Song</BaseButton>
    </div>

    <div class="songs">
      <SongItem v-for="song in songs" :key="song.id" :id="song.id" :song="song" />
    </div>
  </div>
</template>

<style scoped>
.songs-list {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-md);
}

.songs-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-block-end: var(--space-lg);
}

.meta {
  color: var(--color-text-muted);
  font-size: 0.9em;
  margin-block-start: var(--space-xs);
}

.separator {
  margin-inline: var(--space-xs);
}

.songs {
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-lg);
}

.songs :deep(.song-item:last-child) {
  border-bottom: none;
}
</style>