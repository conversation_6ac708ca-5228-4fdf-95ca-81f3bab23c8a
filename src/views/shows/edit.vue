<script setup lang="ts">
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useShows } from '@/composables/useShows'
import SetInput from '@/components/SetInput.vue'
import ShowDurationDisplay from '@/components/ShowDurationDisplay.vue'
import PDFSetList from '@/components/PDFSetList.vue'
import { Show } from '@/models/Show'
import { Check, Plus } from 'lucide-vue-next'

const router = useRouter()
const route = useRoute()
const { saveShow, updateShow, loadShow } = useShows()

const show = ref<Show>({
  id: crypto.randomUUID(),
  title: 'untitled show',
  venue: '',
  act: '',
  date: new Date().toISOString().split('T')[0],
  storageLocation: 'local',
  sets: [{
    id: crypto.randomUUID(),
    name: 'untitled set',
    songs: [{ id: crypto.randomUUID(), title: '', key: '' }]
  }]
})

const isPDFPreviewOpen = ref(false)
const editingShowTitle = ref(false)



// Compute duplicate songs across all sets
const duplicateSongs = computed(() => {
  const songMap = new Map<string, number>()
  const duplicates = new Set<string>()

  show.value.sets.forEach(set => {
    set.songs.forEach(song => {
      if (song.title.trim()) {
        const normalizedTitle = song.title.toLowerCase().trim()
        songMap.set(normalizedTitle, (songMap.get(normalizedTitle) || 0) + 1)
        if (songMap.get(normalizedTitle)! > 1) {
          duplicates.add(normalizedTitle)
        }
      }
    })
  })

  return duplicates
})

// Load show data if editing existing show
onMounted(() => {
  const showId = route.params.id as string

  // If this is not a new show, try to load it
  if (showId && showId !== 'new') {
    const loadedShow = loadShow(showId)
    if (loadedShow) {
      show.value = loadedShow
    } else {
      router.push('/')
    }
  }
})

const handleSave = () => {
  clearEmptyRows()
  const showId = route.params.id as string
  if (showId && showId !== 'new') {
    updateShow(showId, show.value)
  } else {
    saveShow(show.value)
  }
  router.go(-1)
}

const handleCancel = () => {
  router.go(-1)
}

// Helper function for duration display
const getSongSets = (show: Show) => {
  return show.sets.map(set => set.songs)
}

const addSet = () => {
  if (show.value.sets.length < 4) {
    show.value.sets.push({
      id: crypto.randomUUID(),
      name: 'untitled set',
      songs: [{ id: crypto.randomUUID(), title: '', key: '' }]
    })
  }
}

const removeSet = (setIndex: number) => {
  if (show.value.sets.length > 1) {
    show.value.sets.splice(setIndex, 1)
  }
}

const updateSong = (
  setIndex: number,
  songIndex: number,
  field: 'title' | 'key' | 'artist' | 'durationSecs' | 'bpm',
  value: string | number
) => {
  if (setIndex >= 0 && setIndex < show.value.sets.length) {
    const set = show.value.sets[setIndex]
    if (songIndex >= 0 && songIndex < set.songs.length) {
      const parsedValue = field === 'durationSecs' || field === 'bpm' ? Number(value) : value
      set.songs[songIndex][field] = parsedValue
    }
  }
}

const addSong = (setIndex: number, songIndex?: number) => {
  const clickedSongId = (songIndex !== undefined) ? show.value.sets[setIndex].songs[songIndex]?.id : undefined

  clearEmptyRows()

  // Add the new empty song to the target set - check if song index is provided and insert after that song
  const id = crypto.randomUUID()

  const newSongIndex = clickedSongId ? show.value.sets[setIndex].songs.findIndex(song => song.id === clickedSongId) : show.value.sets[setIndex].songs.length

  if (songIndex !== undefined) {
    show.value.sets[setIndex].songs.splice(newSongIndex + 1, 0, {
      id,
      title: '',
      key: ''
    })
  } else {
    show.value.sets[setIndex].songs.push({
      id,
      title: '',
      key: ''
    })
  }

  // Focus on the song title input
  nextTick(() => {
    const input = document.querySelector<HTMLInputElement>(`#title-${id}`)
    if (input) {
      input.focus()
    }
  })
}

const clearEmptyRows = (): void => {
  // First, find and remove any empty song rows across all sets
  let emptyRows: { currentSetIndex: number; emptyRowIndex: number }[] = []

  // Find empty rows in the target set
  show.value.sets.forEach((set, currentSetIndex) => {
    const emptyRowIndex = set.songs.findIndex(song => !song.title.trim() && !song.key.trim())
    if (emptyRowIndex !== -1) {
      emptyRows.unshift({ currentSetIndex, emptyRowIndex })
    }
  })

  // Remove empty rows
  if (emptyRows.length === 0) {
    console.log('No empty rows to remove')
  } else {
    emptyRows.forEach(emptyRowIndex => {
      show.value.sets[emptyRowIndex.currentSetIndex].songs.splice(emptyRowIndex.emptyRowIndex, 1)
    })
  }
}

const removeSong = (setIndex: number, songIndex: number) => {
  show.value.sets[setIndex].songs = show.value.sets[setIndex].songs.filter(
    (_, index) => index !== songIndex
  )
}

const moveSong = (fromSetIndex: number, fromSongIndex: number, toSetIndex: number, toStart: boolean) => {
  const song = { ...show.value.sets[fromSetIndex].songs[fromSongIndex] }

  // Remove from original set
  show.value.sets[fromSetIndex].songs.splice(fromSongIndex, 1)

  // Add to new set
  if (toStart) {
    show.value.sets[toSetIndex].songs.unshift(song)
  } else {
    show.value.sets[toSetIndex].songs.push(song)
  }
}
</script>

<template>
  <div class="show-edit">
    <div class="show-header">
      <h1 @click="() => editingShowTitle = true" v-if="!editingShowTitle">{{ show.title ? show.title : 'Untitled Show'
      }}</h1>
      <div v-else style="display: flex; align-items: center;">
        <input v-model="show.title" @keydown.enter="() => editingShowTitle = false"
          @blur="() => editingShowTitle = false" type="text" class="show-title-input" />
        <BaseButton size="compact" @click="() => editingShowTitle = false">
          <Check />
        </BaseButton>
      </div>
      <ShowDurationDisplay :sets="getSongSets(show)" showLabel />
    </div>


    <div class="form-grid">
      <div class="form-group">
        <label for="venue">Venue</label>
        <input v-model="show.venue" id="venue" type="text" class="input" />
      </div>
      <div class="form-group">
        <label for="act">Act</label>
        <input v-model="show.act" id="act" type="text" class="input" />
      </div>
      <div class="form-group">
        <label for="date">Date</label>
        <input v-model="show.date" id="date" type="date" class="input" />
      </div>
    </div>

    <div class="sets-grid">

      <SetInput v-for="(set, setIndex) in show.sets" :key="set.id" v-model="set.songs" :setIndex :setTitle="set.name"
        :is-last-set="setIndex === show.sets.length - 1" :duplicate-songs="duplicateSongs" @update-song="updateSong"
        @add-song="addSong" @remove-song="removeSong" @remove-set="removeSet" @add-set="addSet" @move-song="moveSong" />


      <BaseCard justify="center" v-if="show.sets.length < 4">
        <BaseButton variant="primary" invert shadow @click="addSet">
          <Plus />
          Add Set
        </BaseButton>
      </BaseCard>

    </div>

    <div class="actions">
      <BaseButton @click="handleCancel" variant="secondary" class="cancel">Cancel</BaseButton>
      <BaseButton @click="isPDFPreviewOpen = !isPDFPreviewOpen" class="preview">
        {{ isPDFPreviewOpen ? 'Hide Preview' : 'Show Preview' }}
      </BaseButton>
      <BaseButton @click="handleSave" class="save">Save Show</BaseButton>
    </div>

    <!-- PDF Preview Panel -->
    <Transition name="slide">
      <PDFSetList v-if="isPDFPreviewOpen" :gigData="show" @close="isPDFPreviewOpen = false" />
    </Transition>
  </div>
</template>

<style scoped>
.show-edit {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-md);
}

.show-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.form-group label {
  font-size: var(--font-size-sm);
  color: var(--color-text-light);
}

.sets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  gap: var(--space-md);
  margin-bottom: var(--space-xl);
}

.actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-md);
}

.button {
  padding: var(--space-sm) var(--space-lg);
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  transition: opacity var(--transition-fast);
}

.button:hover {
  opacity: 0.9;
}

.button.cancel {
  background: var(--color-surface-dark);
  color: var(--color-text-white);
}

.button.save {
  background: var(--color-success);
  color: var(--color-text-white);
}

.button.preview {
  background: var(--color-primary);
  color: var(--color-text-white);
}

.add-set-button {
  background: var(--color-surface);
  border: 2px dashed var(--color-text-light);
  color: var(--color-text-light);
  border-radius: var(--radius-lg);
  padding: var(--space-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  opacity: 0.7;
  transition: all var(--transition-fast);
  height: 100%;
  min-height: 200px;
}

.add-set-button:hover {
  opacity: 1;
  border-color: var(--color-primary);
  color: var(--color-primary);
}

/* Slide animation */
.slide-enter-active,
.slide-leave-active {
  transition: transform var(--transition-normal) ease-out;
}

.slide-enter-from,
.slide-leave-to {
  transform: translateX(100%);
}
</style>