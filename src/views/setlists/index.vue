<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useShows } from '@/composables/useShows'
import { useAuth } from '@/composables/useAuth'
import { Cloud } from 'lucide-vue-next'

import ShowSummary from '@/components/ShowSummary.vue'
import PDFSetList from "@/components/PDFSetList.vue";
const router = useRouter()
const { shows, deleteShow, migrateToCloud, syncCloudShows } = useShows()
const { user } = useAuth()

const selectedShowId = ref<string | null>(null)
const isPreviewOpen = ref(false)
const isSyncing = ref(false)


const handleSelect = (id: string) => {
  selectedShowId.value = id
}

const handleEdit = (id: string) => {
  router.push({ name: 'edit-show', params: { id } })
}

const handlePreview = (id: string) => {
  if (selectedShowId.value === id && isPreviewOpen.value) {
    // Close panel if clicking preview on currently displayed show
    isPreviewOpen.value = false
  } else {
    // Open panel with new show
    selectedShowId.value = id
    isPreviewOpen.value = true
  }
}

const handleNew = () => {
  router.push({ name: 'new-show' })
}

const handlePreviewClose = () => {
  isPreviewOpen.value = false
  selectedShowId.value = null
}

const handleMigrate = async (id: string) => {
  if (await migrateToCloud(id)) {
    // Migration successful
  }
}

const handleSync = async () => {
  if (!user.value) return
  isSyncing.value = true
  try {
    await syncCloudShows()
  } finally {
    isSyncing.value = false
  }
}
</script>

<template>
  <div class="show-list">
    <div class="show-list-header">
      <BaseButton @click="handleNew" variant="primary">
        New Setlist
      </BaseButton>
      <BaseButton v-if="user" @click="handleSync" :disabled="isSyncing" variant="secondary" class="sync-button">
        <Cloud :size="16" />
        {{ isSyncing ? 'Syncing...' : 'Sync Cloud Shows' }}
      </BaseButton>
    </div>

    <div class="shows-grid">
      <ShowSummary v-for="show in shows" :key="show.id" :show="show" @edit="handleEdit" @preview="handlePreview"
        @select="handleSelect" @delete="deleteShow" :is-selected="selectedShowId === show.id"
        :is-preview-open="isPreviewOpen && selectedShowId === show.id" :is-authenticated="!!user"
        @migrate="handleMigrate" />
    </div>

    <!-- PDF Preview Panel -->
    <Transition name="slide">
      <PDFSetList v-if="isPreviewOpen && selectedShowId" :gigData="shows.find(show => show.id === selectedShowId)!"
        @close="handlePreviewClose" />
    </Transition>
  </div>
</template>

<style scoped>
.show-list {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--space-md);
}

.show-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.shows-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-md);
}
</style>