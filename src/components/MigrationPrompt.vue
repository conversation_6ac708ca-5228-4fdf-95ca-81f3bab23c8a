<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useShows } from '@/composables/useShows'
import { Check, X } from 'lucide-vue-next'
import { Show } from '@/models/Show'

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'migrate', ids: string[]): void
}>()

const { shows } = useShows()

const localShows = ref<Show[]>([])
const selectedShows = ref<Set<string>>(new Set())

const toggleShow = (id: string) => {
  if (selectedShows.value.has(id)) {
    selectedShows.value.delete(id)
  } else {
    selectedShows.value.add(id)
  }
}

const selectAll = () => {
  localShows.value.forEach(show => selectedShows.value.add(show.id))
}

const deselectAll = () => {
  selectedShows.value.clear()
}

const showDescription = (show: Show): string => {
  let showDescription = ''

  if (show.venue) {
    showDescription = `@ ${show.venue}`
  }

  if (show.act) {
    showDescription = showDescription ?
      `${showDescription} with ${show.act}` :
      `With ${show.act}`
  }

  return showDescription
}

const handleMigrate = () => {
  emit('migrate', Array.from(selectedShows.value))
  emit('close')
}

onMounted(() => {
  // Fetch local shows and ensure storageLocation is defined
  localShows.value = shows.value.filter(show => show.storageLocation && show.storageLocation === 'local')
})

</script>

<template>
  <div class="migration-prompt">
    <div class="modal-overlay" @click="emit('close')"></div>
    <div class="modal-content">
      <div class="modal-header">
        <h2>Migrate Local Shows</h2>
        <BaseButton variant="ghost" size="tiny" class="close-button" @click="emit('close')">
          <X :size="20" />
        </BaseButton>
      </div>

      <p class="description">
        You have {{ localShows.length }} show{{ localShows.length === 1 ? '' : 's' }} stored locally.
        Would you like to migrate them to the cloud?
      </p>

      <div class="actions">
        <BaseButton size="compact" class="select-all" @click="selectAll">Select All</BaseButton>
        <BaseButton size="compact" class="deselect-all" @click="deselectAll">Deselect All</BaseButton>
      </div>

      <div class="shows-list">
        <BaseCard v-for="show in localShows" :key="show.id" size="compact" shadow @click="toggleShow(show.id)"
          class="show-item-wrapper">
          <div class="show-item">
            <div class="checkbox">
              <Check v-if="selectedShows.has(show.id)" :size="16" />
            </div>
            <div class="show-info">
              <h3>
                {{ show.title || 'Untitled Show' }}
              </h3>
              <h4 v-if="showDescription(show).length">{{ showDescription(show) }}</h4>
              <p>{{ new Date(show.date).toLocaleDateString() }}</p>
            </div>
          </div>
        </BaseCard>
      </div>

      <div class="modal-footer">
        <BaseButton @click="handleMigrate" :disabled="selectedShows.size === 0" variant="primary">
          Migrate Selected Shows
        </BaseButton>
        <BaseButton @click="emit('close')" variant="secondary">
          Skip
        </BaseButton>
      </div>
    </div>
  </div>
</template>

<style scoped>
.migration-prompt {
  position: fixed;
  inset: 0;
  display: grid;
  place-items: center;
  z-index: 1000;
}

.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.modal-content {
  position: relative;
  background: var(--color-surface-muted);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  width: min(90vw, 600px);
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-lg);
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  color: var(--color-text);
}

.close-button {
  background: none;
  border: none;
  color: var(--color-text-light);
  cursor: pointer;
  padding: var(--space-xs);
  border-radius: var(--radius-full);
  display: grid;
  place-items: center;
}

.close-button:hover {
  background: var(--color-surface-muted);
  color: var(--color-text);
}

.description {
  color: var(--color-text-light);
  margin: 0;
}

.actions {
  display: flex;
  gap: var(--space-sm);
}

.actions button {
  background: none;
  border: none;
  color: var(--color-primary);
  cursor: pointer;
  font-size: var(--font-size-sm);
  padding: var(--space-xs);
  border-radius: var(--radius-sm);
}

.actions button:hover {
  background: var(--color-surface-muted);
}

.shows-list {
  display: grid;
  gap: var(--space-xs);
  max-height: 400px;
  padding-block-end: var(--space-sm);
  overflow-y: auto;
}

.show-item-wrapper {
  cursor: pointer;

  &:has(.checkbox:not(:empty)) {
    background-color: var(--color-success-light);
  }
}

.show-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  cursor: pointer;
}

.show-info {
  flex: 1;
}

.show-info h3 {
  margin: 0;
  font-size: var(--font-size-base);
  color: var(--color-text);
}

.show-info p {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--color-text-light);
}

.storage-indicator {
  color: var(--color-text-light);
}

.checkbox {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-text-muted);
  border-radius: var(--radius-sm);
  display: grid;
  place-items: center;
  background-color: var(--color-surface-light);
  color: var(--color-primary);
}

.checkbox:not(:empty) {
  background: var(--color-primary);
  color: white;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--space-sm);
  margin-top: var(--space-md);
}
</style>
