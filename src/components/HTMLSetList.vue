<!-- 
  HTMLSetList.vue
  A component that renders a set list in a printable HTML format that matches A4 dimensions
-->
<script setup lang="ts">
import type { StoredShow, Set } from '@/types/'
import { format } from 'date-fns'
import { ref, onMounted, computed } from 'vue'
import { Printer } from 'lucide-vue-next'

const props = defineProps<{
  show: StoredShow & { branding?: string }
}>()

defineEmits<{
  (e: 'close'): void
}>()

const measureElement = ref<HTMLDivElement | null>(null)

// Initialize measure element after mount
onMounted(() => {
  if (measureElement.value) {
    measureElement.value.style.fontSize = '100pt'
    measureElement.value.style.lineHeight = '1'
  }
})

const formatSetNumber = (num: number): string => {
  return ['ONE', 'TWO', 'THREE', 'FOUR'][num] || String(num + 1)
}

const formatDate = (date: string): string => {
  return format(new Date(date), 'd MMM yy').toUpperCase()
}

const formatSetHeader = (setIndex: number, set: Set): string => {
  const setNumber = formatSetNumber(setIndex)
  const setTitle = set.title ? `: ${set.title.toUpperCase()}` : ''
  return `SET ${setNumber}${setTitle}`
}

// Get the longest song title based on actual rendered width
const getLongestSongTitle = (songs: any[]) => {
  const element = measureElement.value
  if (!element) return { text: '', width: 0 }

  return songs.reduce((longest, song) => {
    const title = song.title.toUpperCase()
    element.textContent = title
    const width = element.offsetWidth
    return width > longest.width ? { text: title, width } : longest
  }, { text: '', width: 0 })
}

// Calculate font size based on available space and longest title
const calculateFontSize = (songs: any[]) => {
  if ((songs.length === 0) || !measureElement.value) return 34

  const AVAILABLE_HEIGHT = 296 - 16 - 25 - 25
  const AVAILABLE_WIDTH = 210 - (16 * 2)

  // Convert mm to pixels (assuming 96 DPI)
  const AVAILABLE_WIDTH_PX = AVAILABLE_WIDTH * 3.7795275591

  // Get the longest song title based on actual width
  const longestSong = getLongestSongTitle(songs)

  // Calculate font size based on width
  // Start with a base size and measure
  measureElement.value.style.fontSize = '100pt'
  const baseWidth = longestSong.width
  const scaleFactor = (AVAILABLE_WIDTH_PX * 0.9) / baseWidth
  const widthBasedSize = 100 * scaleFactor

  // Calculate font size based on height
  const SPACING_FACTOR = 1.2
  const heightBasedSize = (AVAILABLE_HEIGHT / (songs.length * SPACING_FACTOR)) * 2.83

  // Use the smaller of the two sizes to ensure no wrapping
  const calculatedSize = Math.min(widthBasedSize, heightBasedSize)

  return Math.max(34, Math.min(80, calculatedSize))
}

// Split songs into pages based on font size requirements
const splitIntoPages = (songs: any[]) => {
  if (songs.length === 0) return []

  const pages: any[][] = []
  let currentPage: any[] = []

  songs.forEach((song) => {
    // Try adding the song to the current page
    currentPage.push(song)

    // Calculate font size for current page
    const fontSize = calculateFontSize(currentPage)

    // If font size would go below minimum, start a new page
    // (unless this is the first song on the page)
    if (fontSize < 35 && currentPage.length > 1) {
      // Remove the last song from current page
      const lastSong = currentPage.pop()

      // Add current page to pages
      pages.push([...currentPage])

      // Start new page with the last song
      currentPage = [lastSong]
    }
  })

  // Add any remaining songs to the last page
  if (currentPage.length > 0) {
    pages.push(currentPage)
  }

  return pages
}

// Calculate font sizes for all pages in all sets
const setPageStyles = computed(() => {
  return props.show.sets.map(set => {
    const pages = splitIntoPages(set.songs)
    return pages.map(pageSongs => ({
      fontSize: calculateFontSize(pageSongs),
      lineHeight: 1.1
    }))
  })
})

// Get style for a specific song based on its position
const getSongStyle = computed(() => {
  return (setIndex: number, songIndex: number) => {
    // Find which page this song is on
    const pages = splitIntoPages(props.show.sets[setIndex].songs)
    let currentSongCount = 0
    let pageIndex = 0

    // Find the correct page for this song
    for (let i = 0; i < pages.length; i++) {
      if (currentSongCount + pages[i].length > songIndex) {
        pageIndex = i
        break
      }
      currentSongCount += pages[i].length
    }

    // Get the page style, defaulting to base values if not found
    const pageStyle = setPageStyles.value[setIndex]?.[pageIndex] || { fontSize: 34, lineHeight: 1.2 }

    return {
      '--song-font-size': `${pageStyle.fontSize}pt`,
      '--line-height': pageStyle.lineHeight
    }
  }
})

const printSet = (setIndex: number) => {
  const set = props.show.sets[setIndex]
  const pages = splitIntoPages(set.songs)

  // Create a new window for printing
  const printWindow = window.open('', '_blank', 'height=500,width=800')
  if (!printWindow) return

  // Create the print document
  const printDoc = printWindow.document
  printDoc.open()

  // Add complete HTML structure with all necessary styles
  printDoc.write(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>Set ${formatSetNumber(setIndex)}</title>
        <style>
          @page {
            size: A4;
            margin: 0;
          }
          
          body {
            margin: 0;
            font-family: system-ui, -apple-system, sans-serif;
            background: white;
          }

          .a4-page {
            width: 210mm;
            height: 296mm;
            margin: 0 auto;
            position: relative;
            background: white;
            page-break-after: always;
          }

          .a4-page:last-child {
            page-break-after: avoid;
          }

          .set-page {
            height: 296mm;
            padding: 12mm;
            position: relative;
            box-sizing: border-box;
          }

          .page-header {
            font-size: 18pt;
            font-weight: bold;
            text-align: center;
          }

          .page-number {
            font-size: 32pt;
            text-align: center;
            color: #666;
          }

          .song-list {
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: stretch;
            margin-bottom: 20mm;
            line-height: 1.2;
          }

          .song-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0;
            white-space: nowrap;
          }

          .song-item.is-encore {
            font-style: italic;
          }

          .song-key {
            color: #666;
          }

          .page-footer {
            position: absolute;
            bottom: 12mm;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 0 12mm;
          }

          .branding {
            max-width: 100px;
            height: auto;
          }
        </style>
      </head>
      <body>
  `)

  // Add each page
  pages.forEach((page, pageIndex) => {
    const pageFontSize = calculateFontSize(page)
    const pageHtml = `
      <div class="a4-page">
        <div class="set-page">
          <header class="page-header">
            ${props.show.act.toUpperCase()} - ${props.show.venue.toUpperCase()} -
            ${formatDate(props.show.date)} - ${formatSetHeader(setIndex, set)}
          </header>

          ${pages.length > 1 ? `
            <div class="page-number">
              Page ${pageIndex + 1} of ${pages.length}
            </div>
          ` : ''}

          <main class="song-list" style="font-size: ${pageFontSize}pt">
            ${page.map(song => `
              <div class="song-item${song.isEncore ? ' is-encore' : ''}">
                <span class="song-title">${song.title.toUpperCase()}
                  ${song.key ? `<span class="song-key">(${song.key})</span>` : ''}
                </span>
              </div>
            `).join('')}
          </main>

          <footer class="page-footer">
            ${props.show.branding ? `
              <img src="${window.location.origin}${props.show.branding}" 
                   alt="Branding" class="branding">
            ` : ''}
          </footer>
        </div>
      </div>
    `
    printDoc.write(pageHtml)
  })

  // Close the document
  printDoc.write('</body></html>')
  printDoc.close()

  // Handle printing and window closing
  printWindow.onload = () => {
    setTimeout(() => {
      printWindow.print()
      // Close window after printing or if print is cancelled
      const checkWindowClosed = setInterval(() => {
        if (printWindow.closed) {
          clearInterval(checkWindowClosed)
        } else {
          try {
            const isPrinting = printWindow.document.queryCommandEnabled('print')
            if (!isPrinting) {
              printWindow.close()
              clearInterval(checkWindowClosed)
            }
          } catch (e) {
            // Window might be closed already
            clearInterval(checkWindowClosed)
          }
        }
      }, 1000)

      // Fallback - close window after 60 seconds if still open
      setTimeout(() => {
        try {
          if (!printWindow.closed) {
            printWindow.close()
          }
        } catch (e) {
          // Window might be closed already
        }
      }, 60000)
    }, 500)
  }
}
</script>

<template>
  <div class="html-setlist">
    <!-- Hidden element for measurements -->
    <div ref="measureElement" class="measure-element" :style="{
      fontSize: '100pt',
      lineHeight: 1,
      whiteSpace: 'nowrap',
      position: 'absolute',
      visibility: 'hidden',
      padding: 0,
      margin: 0
    }"></div>

    <div class="setlist-panel">
      <div class="panel-header">
        <h2>HTML Preview</h2>
        <div class="header-actions">
          <BaseButton size="compact" variant="ghost" @click="printSet(0)">
            <Printer :size="16" />
          </BaseButton>
          <BaseButton size="compact" variant="ghost" @click="$emit('close')" class="close-button">×</BaseButton>
        </div>
      </div>

      <div class="setlist-pages">
        <template v-for="(set, setIndex) in show.sets" :key="setIndex">
          <div v-for="(page, pageIndex) in splitIntoPages(set.songs)" :key="pageIndex" class="setlist-page">
            <div class="page-header">
              <div class="show-info">
                {{ show.act.toUpperCase() }} - {{ show.venue.toUpperCase() }} - {{ formatDate(show.date) }}
              </div>
              <div class="set-info">
                {{ formatSetHeader(setIndex, set) }}
              </div>
            </div>

            <div class="songs" :style="setPageStyles[setIndex][pageIndex]">
              <div v-for="(song, songIndex) in page" :key="songIndex" class="song"
                :style="getSongStyle(setIndex, songIndex)">
                {{ song.title.toUpperCase() }}
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page-header {
  text-align: center;
  margin-bottom: 25px;
  font-family: var(--font-family-mono);
}

.show-info {
  font-size: 14pt;
  margin-bottom: 4px;
}

.set-info {
  font-size: 16pt;
  font-weight: bold;
  color: var(--color-accent);
}
</style>
