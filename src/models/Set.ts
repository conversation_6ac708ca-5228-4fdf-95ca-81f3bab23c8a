import { ref, computed } from 'vue'
import { Song } from './Song'

export class Set {
  private _id: string
  private _title = ref('')
  private _songs = ref<Song[]>([])
  private _createdAt: string
  private _updatedAt: string

  constructor(data?: any) {
    this._id = data?.id || crypto.randomUUID()
    this._title.value = data?.title || ''
    this._songs.value = (data?.songs || []).map((song: any) =>
      song instanceof Song ? song : new Song(song)
    )
    // Ensure at least one song exists
    if (this._songs.value.length === 0) {
      this._songs.value.push(new Song())
    }
    this._createdAt = data?.createdAt || new Date().toISOString()
    this._updatedAt = data?.updatedAt || new Date().toISOString()
  }

  // Getters
  get id(): string {
    return this._id
  }

  get title(): string {
    return this._title.value
  }

  get songs(): Song[] {
    return this._songs.value
  }

  get createdAt(): string {
    return this._createdAt
  }

  get updatedAt(): string {
    return this._updatedAt
  }

  // Computed properties
  get duration(): number {
    return this._songs.value.reduce(
      (total, song) => total + song.durationSecs,
      0
    )
  }

  get songCount(): number {
    return this._songs.value.length
  }

  get isEmpty(): boolean {
    return (
      this._songs.value.length === 0 ||
      (this._songs.value.length === 1 && this._songs.value[0].isEmpty)
    )
  }

  // Setters
  set title(value: string) {
    this._title.value = value
    this.touch()
  }

  // Methods
  addSong(songData?: any, index?: number): void {
    const newSong = new Song(songData)
    if (typeof index === 'number') {
      this._songs.value.splice(index, 0, newSong)
    } else {
      this._songs.value.push(newSong)
    }
    this.touch()
  }

  removeSong(index: number): void {
    this._songs.value.splice(index, 1)
    if (this._songs.value.length === 0) {
      this.addSong() // Always keep at least one empty song
    }
    this.touch()
  }

  updateSong(index: number, updates: Partial<Song>): void {
    if (index >= 0 && index < this._songs.value.length) {
      const song = this._songs.value[index]
      Object.assign(song, updates)
      this.touch()
    }
  }

  moveSong(fromIndex: number, toIndex: number): void {
    const song = this._songs.value.splice(fromIndex, 1)[0]
    this._songs.value.splice(toIndex, 0, song)
    this.touch()
  }

  // Helper method to update the updatedAt timestamp
  private touch(): void {
    this._updatedAt = new Date().toISOString()
  }

  // Convert to plain object for storage/serialization
  toJSON() {
    return {
      id: this._id,
      title: this._title.value,
      songs: this._songs.value.map(song => song.toJSON()),
      createdAt: this._createdAt,
      updatedAt: this._updatedAt
    }
  }

  // Create a Set instance from a plain object
  static fromJSON(data: any): Set {
    return new Set(data)
  }
}
