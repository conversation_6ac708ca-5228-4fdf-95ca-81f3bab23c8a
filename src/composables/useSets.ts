import { ref, onMounted, watch } from 'vue'
import { Set } from '@/models/Set'
import { useAuth } from '@/composables/useAuth'
import { db } from '@/firebase'
import {
  collection,
  addDoc,
  query,
  where,
  onSnapshot,
  orderBy,
  doc,
  updateDoc,
  deleteDoc,
  getDoc
} from 'firebase/firestore'

export function useSets() {
  const sets = ref<Set[]>([])
  const { user } = useAuth()
  let unsubscribe: (() => void) | undefined

  const subscribeSets = () => {
    // Clean up existing subscription if any
    unsubscribe?.()

    if (!user.value?.uid) {
      sets.value = []
      return
    }

    const setsQuery = query(
      collection(db, 'sets'),
      where('createdBy', '==', user.value.uid),
      orderBy('updatedAt', 'desc')
    )

    unsubscribe = onSnapshot(
      setsQuery,
      snapshot => {
        sets.value = snapshot.docs.map(doc => {
          const data = doc.data()
          return new Set({
            id: doc.id,
            title: data.name || '',
            songs: data.songs || [],
            createdAt: data.createdAt?.toDate().toISOString() || new Date().toISOString(),
            updatedAt: data.updatedAt?.toDate().toISOString() || new Date().toISOString()
          })
        })
      },
      error => {
        console.error('Error fetching sets:', error)
        sets.value = []
      }
    )

    return unsubscribe
  }

  const createSet = async (setData: Partial<Set>): Promise<Set> => {
    if (!user.value?.uid) throw new Error('User not authenticated')

    const newSet = new Set(setData)
    
    const docRef = await addDoc(collection(db, 'sets'), {
      name: newSet.title,
      songs: newSet.songs.map((song, index) => ({
        songId: doc(db, 'songs', song.id),
        order: index + 1,
        isLastSong: song.isLastSong || false
      })),
      createdBy: user.value.uid,
      createdAt: new Date(),
      updatedAt: new Date()
    })
    
    newSet.id = docRef.id
    return newSet
  }

  const updateSet = async (setId: string, updates: Partial<Set>): Promise<void> => {
    if (!user.value?.uid) throw new Error('User not authenticated')

    const setRef = doc(db, 'sets', setId)
    await updateDoc(setRef, {
      ...updates,
      updatedAt: new Date()
    })
  }

  const deleteSet = async (setId: string): Promise<void> => {
    if (!user.value?.uid) throw new Error('User not authenticated')

    const setRef = doc(db, 'sets', setId)
    await deleteDoc(setRef)
  }

  const getSet = async (setId: string): Promise<Set | null> => {
    if (!user.value?.uid) return null

    const setRef = doc(db, 'sets', setId)
    const setDoc = await getDoc(setRef)
    
    if (!setDoc.exists()) return null
    
    const data = setDoc.data()
    return new Set({
      id: setDoc.id,
      title: data.name || '',
      songs: data.songs || [],
      createdAt: data.createdAt?.toDate().toISOString() || new Date().toISOString(),
      updatedAt: data.updatedAt?.toDate().toISOString() || new Date().toISOString()
    })
  }

  // Watch for user changes and resubscribe
  watch(
    () => user.value?.uid,
    () => {
      subscribeSets()
    },
    { immediate: true }
  )

  // Cleanup on unmount
  onMounted(() => {
    return () => unsubscribe?.()
  })

  return {
    sets,
    createSet,
    updateSet,
    deleteSet,
    getSet
  }
}
