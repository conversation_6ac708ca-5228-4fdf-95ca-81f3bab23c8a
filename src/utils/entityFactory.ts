import { Song } from '@/models/Song'
import { Set } from '@/models/Set'
import { Show } from '@/models/Show'

export const createSong = (data?: any): Song => {
  return new Song(data)
}

export const createSet = (data?: any): Set => {
  return new Set(data)
}

export const createShow = (data?: any): Show => {
  return new Show(data)
}

// Legacy functions for backward compatibility
export const createSetList = (data: any = {}) => ({
  id: crypto.randomUUID(),
  name: '',
  sets: [],
  hasEncore: false,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...data
})

export const createVenue = (data: any = {}) => ({
  id: crypto.randomUUID(),
  name: '',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...data
})

export const createArtist = (data: any = {}) => ({
  id: crypto.randomUUID(),
  name: '',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...data
})

export const createAct = (data: any = {}) => ({
  id: crypto.randomUUID(),
  name: '',
  artists: [],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...data
})
